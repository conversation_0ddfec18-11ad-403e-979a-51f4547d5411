import getpass
import socket
import uuid
import aiohttp
import streamlit as st

from config.run_config import API_ACCESS_TOKEN, DOMAIN, RUN_CONFIG_DICT
from core.schema.chat_response import EventType
from data_loader import load_item_name_list
from util.llm_util import translate
from util.common_util import decode_sse, is_empty


async def response_generator(query, product_id, env, request_id):
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/chat"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": "streamlit",
    }
    data = {
        "area": 1,
        "site": 0,
        "category_name": "0",
        "category_id": "0",
        "user_id": "0",
        "item_id": "0",
        "org_id": "0",
        "conversation_id": "0",
        "language": 2,
        "item_name": product_id,
        "chat_history": [{"role": 1, "messages": [{"content": query, "type": 1}]}],
        "response_mode": 1,
        "request_id": request_id,
        "debug": True,
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                yield f"请求失败，状态码: {response.status},响应内容:{await response.text()}"
                return

            async for line in response.content:
                # 去除多余的新行和空行
                chunk = line.decode("utf-8").strip()
                if is_empty(chunk):
                    continue

                response_dict = decode_sse(chunk)
                if is_empty(response_dict) or "event" not in response_dict:
                    continue
                
                cur_event_type = response_dict["event"]
                # if cur_event_type == EventType.START_EVENT.value:
                #     yield "Answer: "

                if cur_event_type == EventType.TEXT_CHUNK_EVENT.value:
                    yield response_dict["data"]["text"]

                if cur_event_type == EventType.FINISH_EVENT.value:
                    request_id = response_dict["request_id"]
<<<<<<< Updated upstream:demo.py
                    first_token_elapse = (
                        response_dict["data"]["answer_start_time"]
                        - response_dict["data"]["request_receive_time"]
                    ) / 1000

                    answer_elapse = (
                        response_dict["data"]["answer_finish_time"]
                        - response_dict["data"]["answer_start_time"]
                    ) / 1000

                    cost_total_tokens = response_dict["data"]["total_tokens"]
                    msg = f'(开始回答耗时={first_token_elapse:.2f}秒, 回答耗时={answer_elapse}s, chatRequestId: "{request_id}", 回答消耗总tokens={cost_total_tokens})'
=======
                    first_token_elapse = int(
                        (
                            response_dict["data"]["answer_start_time"]
                            - response_dict["data"]["request_receive_time"]
                        )
                        / 1000
                    )
                    answer_elapse = int(
                        (
                            response_dict["data"]["answer_finish_time"]
                            - response_dict["data"]["answer_start_time"]
                        )
                        / 1000
                    )
                    msg = f'(开始回答耗时={first_token_elapse}s, 回答耗时={answer_elapse}s, chatRequestId: "{request_id}")'
>>>>>>> Stashed changes:backend.py
                    yield msg
                    return


st.title("国际促销员 Copilot")
path = "config/item_id_name_xiaomi.json"
candidates, _ = load_item_name_list(path)
candidates.append("UNK")

ENV_NAME_DICT = {"本地": "local", "测试": "test", "预发": "preview", "生产": "prod"}

col1, col2, col3 = st.columns([1, 1, 1])
with col1:
    language = st.selectbox("选择语言", ["印尼语", "中文"])
with col2:
    product_id = st.selectbox("选择机型", candidates)
with col3:
    env = st.selectbox("选择环境", ["本地", "测试", "预发"])
    env = ENV_NAME_DICT[env]
debug_by_chinese = language == "中文"

# Initialize chat history
if "messages" not in st.session_state:
    st.session_state.messages = []

# Display chat messages from history on app rerun
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Accept user input
if prompt := st.chat_input("What is up?"):
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})
    if debug_by_chinese:
        translated_prompt = translate(prompt)
        st.session_state.messages.append({"role": "user", "content": translated_prompt})

    # Display user message in chat message container
    request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"
    with st.chat_message("user"):
        st.markdown(f'{prompt}(chatRequestId: "{request_id}")')
        if debug_by_chinese:
<<<<<<< Updated upstream:demo.py
            st.markdown(f'{translated_prompt}(chatRequestId: "{request_id}")')
=======
            st.markdown(st.markdown(f'{translated_prompt}(chatRequestId: "{request_id}")'))
>>>>>>> Stashed changes:backend.py

    # Display assistant response in chat message container
    with st.chat_message("assistant"):
        if debug_by_chinese:
<<<<<<< Updated upstream:demo.py
            response = st.write_stream(
                response_generator(translated_prompt, product_id, env, request_id)
            )
=======
            response = st.write_stream(response_generator(translated_prompt, product_id, env, request_id))
>>>>>>> Stashed changes:backend.py
            st.session_state.messages.append({"role": "assistant", "content": response})
            translated_response = translate(response, from_lang="印尼语", to_lang="中文")
            st.session_state.messages.append(
                {
                    "role": "assistant",
                    "content": translated_response,
                }
            )
            st.markdown(translated_response)
        else:
            response = st.write_stream(response_generator(prompt, product_id, env, request_id))
            st.session_state.messages.append({"role": "assistant", "content": response})
