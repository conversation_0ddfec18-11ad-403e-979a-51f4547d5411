from enum import auto, StrEnum

MODEL_VERSION = "v0.0.6"


class ModelConfigKey(StrEnum):
    API_KEY_TEXT = auto()
    API_KEY_JSON = auto()
    API_KEY_JSON_MIFY2 = auto()
    API_KEY_JSON_MIFY3 = auto()
    DATASET_KEY = auto()
    ITEM_ID_NAME_PATH = auto()
    ITEM_ID_NAME_XIAOMI_PATH = auto()
    ITEM_NAME_DATASET_ID_PATH = auto()
    MINET_INTRO_PATH = auto()
    MINET_PARAM_PATH = auto()
    API_KEY_TRANSLATE = auto()
    PROMPT_PATH = auto()


# 环境 - 配置
# 不保证不同版本的模型配置可兼容
# 这里可以只放「外部」的信息，内部的配置在代码中写死即可
# （其实外部的也可以放到各个地方，只不过根据环境不同，配置也会不同，放在这里统一管理）
MODEL_CONFIG = {
    "test": {
        ModelConfigKey.API_KEY_TEXT: "app-i3y8JTgXhIrU4QPKAasdwVal",
        ModelConfigKey.API_KEY_JSON: "app-nVhf8kgf9FwA7fS6hDUkCJZ7",
        ModelConfigKey.API_KEY_JSON_MIFY2: "app-pyMZ9rscnYtlmQbl72LskqXl",
        ModelConfigKey.API_KEY_JSON_MIFY3: "app-9ArZ4UMtsAOkGZj9BurTQ977",
        ModelConfigKey.DATASET_KEY: "dataset-TsPXNbDYMXJw4a8iQng4ACch",
        ModelConfigKey.ITEM_ID_NAME_PATH: "./config/item_id_name.json",
        ModelConfigKey.ITEM_ID_NAME_XIAOMI_PATH: "./config/item_id_name_xiaomi.json",
        ModelConfigKey.ITEM_NAME_DATASET_ID_PATH: "./config/item_name_dataset_id_test.json",
        ModelConfigKey.MINET_INTRO_PATH: "./config/minet_intro.json",
        ModelConfigKey.MINET_PARAM_PATH: "./config/minet_param.json",
        ModelConfigKey.API_KEY_TRANSLATE: "app-EgMaEObyPWEt40se94WTkSBE",
        ModelConfigKey.PROMPT_PATH: "./config/prompts.toml",
    },
    "preview": {
        ModelConfigKey.API_KEY_TEXT: "app-lA3ratFrE52hPxJhbqmlSd4l",
        ModelConfigKey.API_KEY_JSON: "app-kSYYkZRAiVkmNO8oaUhfJ9wb",
        ModelConfigKey.API_KEY_JSON_MIFY2: "app-wht2dLhRtPIU1oPgnDp5uHk8",
        ModelConfigKey.API_KEY_JSON_MIFY3: "app-y6efZxNom6sR2Z0k1Rb5wLBv",
        ModelConfigKey.DATASET_KEY: "dataset-jw0XHS9hJ8w6AoSVquSWlunR",
        ModelConfigKey.ITEM_ID_NAME_PATH: "./config/item_id_name.json",
        ModelConfigKey.ITEM_ID_NAME_XIAOMI_PATH: "./config/item_id_name_xiaomi.json",
        ModelConfigKey.ITEM_NAME_DATASET_ID_PATH: "./config/item_name_dataset_id_preview.json",
        ModelConfigKey.MINET_INTRO_PATH: "./config/minet_intro.json",
        ModelConfigKey.MINET_PARAM_PATH: "./config/minet_param.json",
        ModelConfigKey.API_KEY_TRANSLATE: "app-FNYKIGUUqRe5qNJCxtE1DIuF",
        ModelConfigKey.PROMPT_PATH: "./config/prompts.toml",
    },
    "prod": {
        ModelConfigKey.API_KEY_TEXT: "app-IdzRFSEQpAAyzZGTbUgRHEEZ",
        ModelConfigKey.API_KEY_JSON: "app-D0dxL946QzZuTHk1eHOXWGra",
        ModelConfigKey.API_KEY_JSON_MIFY2: "app-l6BUpE0yXJHKThFDFjlZHcRp",
        ModelConfigKey.API_KEY_JSON_MIFY3: "app-CHF3ws1k9aIGGuO6SQHMWHvt",
        ModelConfigKey.DATASET_KEY: "dataset-G2EyyAhamqJZJwViVooyKY8v",
        ModelConfigKey.ITEM_ID_NAME_PATH: "./config/item_id_name.json",
        ModelConfigKey.ITEM_ID_NAME_XIAOMI_PATH: "./config/item_id_name_xiaomi.json",
        ModelConfigKey.ITEM_NAME_DATASET_ID_PATH: "./config/item_name_dataset_id_prod.json",
        ModelConfigKey.MINET_INTRO_PATH: "./config/minet_intro.json",
        ModelConfigKey.MINET_PARAM_PATH: "./config/minet_param.json",
        ModelConfigKey.API_KEY_TRANSLATE: "app-FTZNWifEj8ttCUVrhZxPOcES",
        ModelConfigKey.PROMPT_PATH: "./config/prompts.toml",
    },
}
