# 回归测试页面缓存功能说明

## 功能概述

为了提升用户体验，回归测试页面现在支持配置缓存功能。当用户点击"运行"按钮时，页面会自动保存当前的所有输入配置，下次进入页面时会自动加载这些配置，避免重复输入。

## 缓存的配置项

以下配置项会被自动缓存：

### 基本配置
- **环境选择**: 本地/测试/预发/生产
- **数据行选择**: 起始行号和结束行号
- **批量大小**: 每次并发请求的数量
- **评测分数标志**: 是否评测分数

### 列选择配置
- **产品名称列**: 选择的产品列名
- **问题列**: 选择的问题列名  
- **参考答案列**: 选择的参考答案列名（可选）
- **额外保留列**: 多选的额外保留列列表

### 高级配置
- **请求超时时间**: 单位秒
- **失败重试次数**: 重试次数
- **参数版本**: version参数

### 文件配置
- **自定义文件名后缀**: 输出文件的后缀名

## 使用方式

### 自动保存
1. 在页面上配置各种参数
2. 点击"运行"按钮
3. 系统会自动保存当前配置并显示"✅ 配置已保存，下次进入页面将自动加载这些设置"

### 自动加载
1. 下次进入回归测试页面时
2. 系统会自动加载上次保存的配置
3. 所有输入框和选择框会显示上次的值

### 清除缓存
1. 在左侧边栏找到"配置管理"部分
2. 点击"清除缓存配置"按钮
3. 系统会清除保存的配置，下次将使用默认值

## 智能适配

### 文件列适配
当加载新的Excel文件时，系统会智能适配缓存的列选择：
- 如果缓存的列名在新文件中存在，则使用缓存值
- 如果缓存的列名不存在，则使用智能匹配或默认值
- 额外保留列会自动过滤掉不存在的列

### 行数适配
- 如果缓存的结束行号超过新文件的总行数，会自动调整为文件的最大行数
- 起始行号会确保在有效范围内

## 技术实现

### 缓存存储
- 配置文件存储在 `tmp/cache/` 目录下
- 文件名格式: `regression_test_config.json`
- 使用JSON格式存储，支持中文

### 数据结构
使用Pydantic BaseModel定义配置结构，确保数据类型安全：

```python
class RegressionTestConfig(BaseModel):
    env: str = "预发"
    start_row: int = 1
    end_row: Optional[int] = None
    batch_size: int = 10
    val_score_flag: bool = False
    product_str: str = "SPU"
    query_str: str = ""
    ref_ans: str = ""
    extra_columns_list: list = []
    timeout: int = 60
    max_retries: int = 2
    version: int = 1
    custom_suffix: str = "default_suffix"
```

## 注意事项

1. **文件上传不缓存**: 上传的文件路径不会被缓存，每次需要重新选择文件
2. **配置持久化**: 配置会持久保存，直到手动清除或被新配置覆盖
3. **多用户隔离**: 每个用户的配置独立存储（基于项目目录）
4. **错误处理**: 如果缓存文件损坏，系统会自动使用默认配置

## 扩展性

该缓存系统设计为通用框架，可以轻松扩展到其他页面：
- 只需定义新的配置类
- 使用相同的CacheManager进行管理
- 支持多页面独立缓存
